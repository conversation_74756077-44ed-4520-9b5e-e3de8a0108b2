import React, { useState } from 'react';
import { View, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Link, router } from 'expo-router';
import { useSignIn } from '@clerk/clerk-expo';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Input } from '@/components/ui/input';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const { signIn } = useSignIn();

  const handleResetPassword = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    setLoading(true);
    try {
      // Use Clerk's password reset flow
      await signIn?.create({
        strategy: 'reset_password_email_code',
        identifier: email,
      });

      Alert.alert(
        'Reset Email Sent',
        'Check your email for password reset instructions.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error: any) {
      console.error('Password reset error:', error);
      Alert.alert(
        'Reset Failed',
        error?.errors?.[0]?.message || 'An unexpected error occurred'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-background"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View className="flex-1 justify-center px-6">
        <Text className="text-3xl font-bold text-foreground mb-2 text-center">
          Reset Password
        </Text>
        <Text className="text-base text-muted-foreground mb-8 text-center leading-6">
          Enter your email address and we&apos;ll send you instructions to reset
          your password.
        </Text>

        <Input
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          autoComplete="email"
          className="mb-6"
        />

        <Button
          onPress={handleResetPassword}
          disabled={loading}
          className="mb-6"
        >
          <Text>{loading ? 'Sending...' : 'Send Reset Instructions'}</Text>
        </Button>

        <View className="flex-row justify-center items-center">
          <Text className="text-muted-foreground text-sm">
            Remember your password?{' '}
          </Text>
          <Link href="/(auth)/sign-in" asChild>
            <Button variant="link" size="sm">
              <Text className="text-primary font-semibold">Sign In</Text>
            </Button>
          </Link>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}
