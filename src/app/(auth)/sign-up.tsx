import * as React from 'react';
import {
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { isClerkAPIResponseError, useSignUp } from '@clerk/clerk-expo';
import { Link, useRouter } from 'expo-router';
import { useSignUpForm } from '@/lib/hooks/useSignUpForm';
import { Controller } from 'react-hook-form';
import { AlertCircle } from 'lucide-react-native';
import Dropdown from '@/components/onboarding/Dropdown';
import { ROLE_OPTIONS, SignUpFormData } from '@/types/auth';
import { ClerkAPIError } from '@clerk/types';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Input } from '@/components/ui/input';

export default function SignUpScreen() {
  const router = useRouter();
  const { isLoaded, signUp } = useSignUp();

  const {
    watch,
    control,
    setValue,
    loading,
    // onSubmit,
    handleSubmit,
    formState: { errors, isValid },
  } = useSignUpForm();

  // Watch form values for real-time updates
  const watchedValues = watch();

  const [clerkErrors, setClerkErrors] = React.useState<ClerkAPIError[]>([]);
  const [pendingVerification, setPendingVerification] = React.useState(false);

  // Handle submission of sign-up form
  const onSignUpPress = React.useCallback(
    async (data: SignUpFormData) => {
      if (!isLoaded) return;

      // Clear any errors that may have occurred during previous form submission
      setClerkErrors([]);

      const { emailAddress, password } = data;

      // Start sign-up process using email and password provided
      try {
        const signUpAttempt = await signUp.create({
          emailAddress,
          password,
        });

        // Send user an email with verification code
        await signUp.prepareEmailAddressVerification({
          strategy: 'email_code',
        });

        // Set 'pendingVerification' to true to display second form
        // and capture OTP code
        setPendingVerification(true);

        if (signUpAttempt.status === 'complete') {
          Alert.alert(
            'Success',
            'Account created! Please check your email for verification.',
            [
              {
                text: 'OK',
                onPress: () => router.replace('/(auth)/sign-in'),
              },
            ]
          );
        }
      } catch (err) {
        // See https://clerk.com/docs/custom-flows/error-handling
        // for more info on error handling
        console.error(JSON.stringify(err, null, 2));
        Alert.alert('Error', 'An unexpected error occurred');

        if (isClerkAPIResponseError(err)) setClerkErrors(err.errors);
        console.error(JSON.stringify(err, null, 2));
      }
    },
    [isLoaded, router, signUp]
  );

  if (pendingVerification) {
    return <VerifyEmailScreen />;
  }

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-background"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView className="flex-1" keyboardShouldPersistTaps="handled">
        <View className="px-6 py-10">
          <Text className="text-3xl font-bold text-foreground mb-2 text-center">
            Create Account
          </Text>
          <Text className="text-base text-muted-foreground mb-8 text-center">
            Join us in your liver health journey
          </Text>

          <Controller
            name="fullName"
            control={control}
            rules={{ required: true }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                label="Full Name"
                required
                placeholder="Full Name"
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                autoCapitalize="words"
                autoComplete="name"
                error={errors.fullName?.message}
              />
            )}
          />

          <Controller
            name="emailAddress"
            control={control}
            rules={{ required: true }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                required
                label="Email Address"
                placeholder="Email Address"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                error={errors.emailAddress?.message}
              />
            )}
          />

          <Dropdown
            label="Role"
            value={watchedValues.role || 'patient'}
            options={[...ROLE_OPTIONS]}
            onSelect={value =>
              setValue(
                'role',
                value as 'patient' | 'caregiver' | 'healthcare_provider'
              )
            }
            placeholder="Select your role"
            error={errors.role?.message}
            required
            accessibilityLabel="Role selection dropdown"
          />

          <Controller
            name="password"
            control={control}
            rules={{ required: true }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                required
                secureTextEntry
                placeholder="Password"
                label="Password"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                autoComplete="new-password"
                error={errors.password?.message}
              />
            )}
          />

          <Controller
            name="confirmPassword"
            control={control}
            rules={{ required: true }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                required
                secureTextEntry
                label="Confirm Password"
                placeholder="Confirm Password"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                autoComplete="new-password"
                error={errors.confirmPassword?.message}
              />
            )}
          />

          <Button
            onPress={handleSubmit(onSignUpPress)}
            disabled={loading || !isValid}
            className="mt-3 mb-6"
          >
            <Text>{loading ? 'Creating Account...' : 'Create Account'}</Text>
          </Button>

          <View className="flex-row justify-center items-center">
            <Text className="text-muted-foreground text-sm">
              Already have an account?{' '}
            </Text>
            <Link href="/(auth)/sign-in" asChild>
              <Button variant="link" size="sm">
                <Text className="text-primary font-semibold">Sign In</Text>
              </Button>
            </Link>
          </View>

          {clerkErrors.length > 0 && (
            <View className="flex-row items-center mt-4 p-3 bg-destructive/10 rounded-lg">
              <AlertCircle size={16} className="text-destructive mr-2" />
              <Text className="text-destructive text-sm flex-1">
                {clerkErrors.map(el => el.longMessage).join(', ')}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

function VerifyEmailScreen() {
  const router = useRouter();
  const { isLoaded, signUp, setActive } = useSignUp();

  const [code, setCode] = React.useState('');

  // Handle submission of verification form
  const onVerifyPress = React.useCallback(async () => {
    if (!isLoaded) return;

    try {
      // Use the code the user provided to attempt verification
      const signUpAttempt = await signUp.attemptEmailAddressVerification({
        code,
      });

      // If verification was completed, set the session to active
      // and redirect the user
      if (signUpAttempt.status === 'complete') {
        await setActive({ session: signUpAttempt.createdSessionId });
        router.replace('/');
      } else {
        // If the status is not complete, check why. User may need to
        // complete further steps.
        console.error(JSON.stringify(signUpAttempt, null, 2));
      }
    } catch (err) {
      // See https://clerk.com/docs/custom-flows/error-handling
      // for more info on error handling
      console.error(JSON.stringify(err, null, 2));
    }
  }, [isLoaded, signUp, code, setActive, router]);

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-background"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="px-6 py-10">
          <Text className="text-3xl font-bold text-foreground mb-2 text-center">
            Verify Your Email
          </Text>
          <Text className="text-base text-muted-foreground mb-8 text-center">
            We&apos;ve sent a verification code to your email address. Please
            enter it below to complete your account setup.
          </Text>

          <Input
            label="Verification Code"
            required
            value={code}
            placeholder="Enter your verification code"
            onChangeText={code => setCode(code)}
            keyboardType="number-pad"
            autoComplete="one-time-code"
          />

          <Button onPress={onVerifyPress} disabled={!code} className="mt-6">
            <Text>Verify Email</Text>
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
