import { useSignIn } from '@clerk/clerk-expo';
import { Link, useRouter } from 'expo-router';
import { View, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Input } from '@/components/ui/input';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod/v4';
import { zodResolver } from '@hookform/resolvers/zod';

const schema = z.object({
  email: z.email(),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters long' }),
});

export default function Page() {
  const router = useRouter();
  const { signIn, setActive, isLoaded } = useSignIn();
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    resolver: zodR<PERSON>olver(schema),
  });

  // Handle the submission of the sign-in form
  const onSignInPress = async (data: z.infer<typeof schema>) => {
    if (!isLoaded) return;

    // Start the sign-in process using the email and password provided
    try {
      const signInAttempt = await signIn.create({
        password: data.password,
        identifier: data.email,
      });

      // If sign-in process is complete, set the created session as active
      // and redirect the user
      if (signInAttempt.status === 'complete') {
        await setActive({ session: signInAttempt.createdSessionId });
        router.replace('/');
      } else {
        // If the status isn't complete, check why. User might need to
        // complete further steps.
        console.error(JSON.stringify(signInAttempt, null, 2));
      }
    } catch (err) {
      // See https://clerk.com/docs/custom-flows/error-handling
      // for more info on error handling
      console.error(JSON.stringify(err, null, 2));
      Alert.alert('Error', 'An unexpected error occurred');
    }

    setLoading(false);
  };

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-background"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View className="flex-1 justify-center px-6">
        <Text className="text-3xl font-bold text-foreground mb-2 text-center">
          Welcome Back
        </Text>
        <Text className="text-base text-muted-foreground mb-8 text-center">
          Sign in to continue your liver health journey
        </Text>

        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <Input
              placeholder="Email"
              value={field.value}
              onChangeText={field.onChange}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              error={errors.email ? errors.email.message : undefined}
            />
          )}
        />

        <Controller
          name="password"
          control={control}
          render={({ field }) => (
            <Input
              secureTextEntry
              placeholder="Password"
              value={field.value}
              onChangeText={field.onChange}
              autoComplete="password"
              error={errors.password ? errors.password.message : undefined}
            />
          )}
        />

        <Button
          onPress={handleSubmit(onSignInPress)}
          disabled={loading}
          className="mb-4"
        >
          <Text>{loading ? 'Signing In...' : 'Sign In'}</Text>
        </Button>

        <Link href="/(auth)/forgot-password" asChild>
          <Button variant="link" className="mb-6">
            <Text className="text-primary">Forgot Password?</Text>
          </Button>
        </Link>

        <View className="flex-row justify-center items-center">
          <Text className="text-muted-foreground text-sm">
            Don&apos;t have an account?{' '}
          </Text>
          <Link href="/(auth)/sign-up" asChild>
            <Button variant="link" size="sm">
              <Text className="text-primary font-semibold">Sign Up</Text>
            </Button>
          </Link>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}
