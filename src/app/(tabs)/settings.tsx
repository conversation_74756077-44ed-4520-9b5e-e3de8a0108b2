import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
} from 'react-native';
import {
  User,
  Bell,
  Shield,
  Heart,
  CircleHelp as HelpCircle,
  FileText,
  Smartphone,
  ChevronRight,
  CreditCard as Edit,
  Save,
  Moon,
  Sun,
  Monitor,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  useAuthUser,
  useAuthActions,
  useSettings,
  useSettingsActions,
} from '@/lib/store/selectors';

export default function SettingsScreen() {
  const user = useAuthUser();
  const { updateProfile } = useAuthActions();

  const settings = useSettings();
  const { updateThemePreference, updateNotificationSettings } =
    useSettingsActions();

  const [editingProfile, setEditingProfile] = useState(false);
  const [localProfile, setLocalProfile] = useState({
    name: user ? `${user.first_name} ${user.last_name}` : '',
    email: user?.email ?? '',
    phone: user?.phone ?? '',
    date_of_birth: user?.date_of_birth ?? '',
  });

  const saveProfile = async () => {
    if (user) {
      const [firstName, ...lastNameParts] = localProfile.name.split(' ');
      const lastName = lastNameParts.join(' ');

      try {
        const { error } = await updateProfile({
          first_name: firstName,
          last_name: lastName,
          email: localProfile.email,
          phone: localProfile.phone,
          date_of_birth: localProfile.date_of_birth,
        });

        if (error) {
          Alert.alert('Error', 'Failed to update profile');
          return;
        }

        setEditingProfile(false);
        Alert.alert('Success', 'Profile updated successfully');
      } catch (error) {
        Alert.alert('Error', 'Failed to update profile');
      }
    }
  };

  const exportData = () => {
    Alert.alert(
      'Export Data',
      'Your health data will be exported as a PDF report. This may take a few moments.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Export', onPress: () => console.log('Exporting data...') },
      ]
    );
  };

  const clearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your health data. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => console.log('Clearing data...'),
        },
      ]
    );
  };

  const showPrivacyPolicy = () => {
    Alert.alert(
      'Privacy Policy',
      'This app follows HIPAA compliance guidelines. Your health data is encrypted and stored securely on your device. We do not share your personal information with third parties without your explicit consent.',
      [{ text: 'OK' }]
    );
  };

  const showHelp = () => {
    Alert.alert(
      'Help & Support',
      'For technical support, contact: <EMAIL>\n\nFor medical questions, consult your healthcare provider.',
      [{ text: 'OK' }]
    );
  };

  const getThemeIcon = (mode: string) => {
    switch (mode) {
      case 'light':
        return <Sun size={20} className="text-primary" />;
      case 'dark':
        return <Moon size={20} className="text-primary" />;
      case 'system':
        return <Monitor size={20} className="text-muted-foreground" />;
    }
  };

  const getThemeLabel = (mode: string) => {
    switch (mode) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
    }
  };

  const showThemeSelector = () => {
    Alert.alert('Choose Theme', 'Select your preferred theme mode', [
      { text: 'Light', onPress: () => updateThemePreference('light') },
      { text: 'Dark', onPress: () => updateThemePreference('dark') },
      { text: 'System', onPress: () => updateThemePreference('system') },
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  const SettingRow = ({
    icon,
    title,
    subtitle,
    onPress,
    rightElement,
    showChevron = true,
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
    showChevron?: boolean;
  }) => (
    <TouchableOpacity
      className="flex-row items-center justify-between px-4 py-4 border-b border-border/30"
      onPress={onPress}
    >
      <View className="flex-row items-center flex-1">
        <View className="mr-3">{icon}</View>
        <View className="flex-1">
          <Text className="font-medium text-base text-foreground mb-0.5">
            {title}
          </Text>
          {subtitle && (
            <Text className="font-normal text-sm text-muted-foreground">
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      <View className="flex-row items-center">
        {rightElement}
        {showChevron && (
          <ChevronRight size={20} className="text-muted-foreground" />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="px-5 pt-4 pb-6">
          <Text className="font-bold text-3xl text-foreground mb-1">
            Settings
          </Text>
          <Text className="font-normal text-base text-muted-foreground">
            Manage your health profile
          </Text>
        </View>

        {/* Profile Section */}
        <View className="px-5 mb-6">
          <View className="flex-row items-center mb-3">
            <User size={20} className="text-primary" />
            <Text className="font-semibold text-lg text-foreground ml-2 flex-1">
              Profile Information
            </Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => setEditingProfile(!editingProfile)}
            >
              {editingProfile ? (
                <Save size={18} className="text-primary" />
              ) : (
                <Edit size={18} className="text-primary" />
              )}
            </TouchableOpacity>
          </View>

          <View className="bg-card rounded-xl p-5 shadow-sm">
            {editingProfile ? (
              <View className="gap-4">
                <View>
                  <Text className="font-medium text-sm text-foreground mb-2">
                    Full Name
                  </Text>
                  <TextInput
                    className="border border-border bg-background text-foreground px-4 py-3 rounded-lg font-normal text-base"
                    value={localProfile.name}
                    onChangeText={text =>
                      setLocalProfile({ ...localProfile, name: text })
                    }
                    placeholder="Enter your full name"
                    placeholderTextColor="#9CA3AF"
                  />
                </View>

                <View>
                  <Text className="font-medium text-sm text-foreground mb-2">
                    Email
                  </Text>
                  <TextInput
                    className="border border-border bg-background text-foreground px-4 py-3 rounded-lg font-normal text-base"
                    value={localProfile.email}
                    onChangeText={text =>
                      setLocalProfile({ ...localProfile, email: text })
                    }
                    placeholder="Enter your email"
                    keyboardType="email-address"
                    placeholderTextColor="#9CA3AF"
                  />
                </View>

                <View>
                  <Text className="font-medium text-sm text-foreground mb-2">
                    Phone
                  </Text>
                  <TextInput
                    className="border border-border bg-background text-foreground px-4 py-3 rounded-lg font-normal text-base"
                    value={localProfile.phone}
                    onChangeText={text =>
                      setLocalProfile({ ...localProfile, phone: text })
                    }
                    placeholder="Enter your phone number"
                    keyboardType="phone-pad"
                    placeholderTextColor="#9CA3AF"
                  />
                </View>

                <View>
                  <Text className="font-medium text-sm text-foreground mb-2">
                    Date of Birth
                  </Text>
                  <TextInput
                    className="border border-border bg-background text-foreground px-4 py-3 rounded-lg font-normal text-base"
                    value={localProfile.date_of_birth}
                    onChangeText={text =>
                      setLocalProfile({ ...localProfile, date_of_birth: text })
                    }
                    placeholder="YYYY-MM-DD"
                    placeholderTextColor="#9CA3AF"
                  />
                </View>

                <TouchableOpacity
                  className="bg-primary py-4 rounded-lg mt-2"
                  onPress={saveProfile}
                >
                  <Text className="font-semibold text-base text-primary-foreground text-center">
                    Save Changes
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View className="gap-4">
                <View>
                  <Text className="font-normal text-sm text-muted-foreground mb-1">
                    Name
                  </Text>
                  <Text className="font-medium text-base text-foreground">
                    {localProfile.name}
                  </Text>
                </View>
                <View>
                  <Text className="font-normal text-sm text-muted-foreground mb-1">
                    Email
                  </Text>
                  <Text className="font-medium text-base text-foreground">
                    {localProfile.email}
                  </Text>
                </View>
                <View>
                  <Text className="font-normal text-sm text-muted-foreground mb-1">
                    Phone
                  </Text>
                  <Text className="font-medium text-base text-foreground">
                    {localProfile.phone}
                  </Text>
                </View>
                <View>
                  <Text className="font-normal text-sm text-muted-foreground mb-1">
                    Date of Birth
                  </Text>
                  <Text className="font-medium text-base text-foreground">
                    {localProfile.date_of_birth}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Appearance Section */}
        <View className="px-5 mb-6">
          <View className="flex-row items-center mb-3">
            {getThemeIcon(settings.themePreference)}
            <Text className="font-semibold text-lg text-foreground ml-2">
              Appearance
            </Text>
          </View>

          <View className="bg-card rounded-xl shadow-sm overflow-hidden">
            <SettingRow
              icon={getThemeIcon(settings.themePreference)}
              title="Theme"
              subtitle={`Currently using ${getThemeLabel(
                settings.themePreference
              )?.toLowerCase()} mode`}
              onPress={showThemeSelector}
            />
          </View>
        </View>

        {/* Notifications Section */}
        <View className="px-5 mb-6">
          <View className="flex-row items-center mb-3">
            <Bell size={20} className="text-primary" />
            <Text className="font-semibold text-lg text-foreground ml-2">
              Notifications
            </Text>
          </View>

          <View className="bg-card rounded-xl shadow-sm overflow-hidden">
            <SettingRow
              icon={<Bell size={20} className="text-muted-foreground" />}
              title="Medication Reminders"
              subtitle="Get notified when it's time for medications"
              rightElement={
                <Switch
                  value={settings.notifications.medicationReminders}
                  onValueChange={value =>
                    updateNotificationSettings({ medicationReminders: value })
                  }
                />
              }
              showChevron={false}
            />

            <SettingRow
              icon={<Heart size={20} className="text-muted-foreground" />}
              title="Meal Reminders"
              subtitle="Reminders for meal times and tracking"
              rightElement={
                <Switch
                  value={settings.notifications.mealReminders}
                  onValueChange={value =>
                    updateNotificationSettings({ mealReminders: value })
                  }
                />
              }
              showChevron={false}
            />

            <SettingRow
              icon={<FileText size={20} className="text-muted-foreground" />}
              title="Weekly Reports"
              subtitle="Summary of your weekly progress"
              rightElement={
                <Switch
                  value={settings.notifications.weeklyReports}
                  onValueChange={value =>
                    updateNotificationSettings({ weeklyReports: value })
                  }
                />
              }
              showChevron={false}
            />
          </View>
        </View>

        {/* Data & Privacy Section */}
        <View className="px-5 mb-6">
          <View className="flex-row items-center mb-3">
            <Shield size={20} className="text-primary" />
            <Text className="font-semibold text-lg text-foreground ml-2">
              Data & Privacy
            </Text>
          </View>

          <View className="bg-card rounded-xl shadow-sm overflow-hidden">
            <SettingRow
              icon={<FileText size={20} className="text-muted-foreground" />}
              title="Export Health Data"
              subtitle="Generate a comprehensive health report"
              onPress={exportData}
            />

            <SettingRow
              icon={<Shield size={20} className="text-muted-foreground" />}
              title="Privacy Policy"
              subtitle="Learn how we protect your data"
              onPress={showPrivacyPolicy}
            />

            <SettingRow
              icon={<Smartphone size={20} className="text-muted-foreground" />}
              title="Data Storage"
              subtitle="Your data is stored securely on device"
            />
          </View>
        </View>

        {/* Support Section */}
        <View className="px-5 mb-6">
          <View className="flex-row items-center mb-3">
            <HelpCircle size={20} className="text-primary" />
            <Text className="font-semibold text-lg text-foreground ml-2">
              Support
            </Text>
          </View>

          <View className="bg-card rounded-xl shadow-sm overflow-hidden">
            <SettingRow
              icon={<HelpCircle size={20} className="text-muted-foreground" />}
              title="Help & Support"
              subtitle="Get help with using the app"
              onPress={showHelp}
            />

            <SettingRow
              icon={<FileText size={20} className="text-muted-foreground" />}
              title="About"
              subtitle="Version 1.0.0 • Liver Health Companion"
            />
          </View>
        </View>

        {/* Danger Zone */}
        <View className="px-5 mb-6">
          <View className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-xl p-5">
            <Text className="font-semibold text-lg text-red-600 dark:text-red-400 mb-4">
              Danger Zone
            </Text>
            <TouchableOpacity
              className="bg-red-600 py-3 px-6 rounded-lg"
              onPress={clearData}
            >
              <Text className="font-semibold text-base text-white text-center">
                Clear All Data
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
