import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import {
  Camera,
  Search,
  Plus,
  TriangleAlert as Alert<PERSON><PERSON>gle,
  CircleCheck as CheckCircle,
  Circle as XCircle,
  Utensils,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CameraView, useCameraPermissions } from 'expo-camera';
import {
  useNutritionActions,
  useMealLogs,
  useUIActions,
  useModals,
  useValidation,
  useDailyNutritionMemo,
} from '@/lib/store/selectors';
import { FOOD_DATABASE } from '@/lib/data/foodDatabase';
import { FoodItem } from '@/lib/store/types';

export default function FoodTrackingScreen() {
  const { openModal, closeModal } = useUIActions();
  const modals = useModals();

  const nutrients = useDailyNutritionMemo();
  const { addFoodWithValidation } = useValidation();
  const { searchFoodDatabase } = useNutritionActions();
  const getMealLogs = useMealLogs;

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMealType, setSelectedMealType] = useState<
    'breakfast' | 'lunch' | 'dinner' | 'snack'
  >('breakfast');
  const [permission, requestPermission] = useCameraPermissions();

  const filteredFoods = searchFoodDatabase(searchQuery);

  const addFoodToLog = (foodItem: FoodItem) => {
    addFoodWithValidation(foodItem, selectedMealType);
    closeModal('addFood');
  };

  const openCamera = async () => {
    if (!permission) {
      const { granted } = await requestPermission();
      if (!granted) {
        Alert.alert(
          'Permission needed',
          'Camera permission is required to scan barcodes'
        );
        return;
      }
    }
    openModal('camera');
  };

  const handleBarcodeScan = (data: any) => {
    closeModal('camera');
    // Simulate barcode lookup
    Alert.alert(
      'Barcode Scanned',
      'Food item found: Canned Tomato Soup\nHigh sodium content detected!',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Add to Log', onPress: () => addFoodToLog(FOOD_DATABASE[1]) },
      ]
    );
  };

  // nutrients now comes from the hook above

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="px-5 pt-4 pb-6">
          <Text className="font-bold text-3xl text-foreground mb-1">
            Food Tracking
          </Text>
          <Text className="font-normal text-base text-muted-foreground">
            Monitor your liver-friendly nutrition
          </Text>
        </View>

        {/* Daily Summary */}
        <View className="mx-5 bg-card rounded-2xl p-5 mb-6 shadow-sm">
          <Text className="font-semibold text-lg text-foreground mb-4">
            Today&apos;s Intake
          </Text>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text
                className={`font-bold text-lg mb-1 ${
                  nutrients.sodium > 2000 ? 'text-red-500' : 'text-green-500'
                }`}
              >
                {nutrients.sodium}mg
              </Text>
              <Text className="font-normal text-xs text-muted-foreground">
                Sodium
              </Text>
            </View>
            <View className="items-center">
              <Text className="font-bold text-lg mb-1 text-teal-500">
                {nutrients.protein}g
              </Text>
              <Text className="font-normal text-xs text-muted-foreground">
                Protein
              </Text>
            </View>
            <View className="items-center">
              <Text className="font-bold text-lg mb-1 text-amber-500">
                {nutrients.fat}g
              </Text>
              <Text className="font-normal text-xs text-muted-foreground">
                Fat
              </Text>
            </View>
            <View className="items-center">
              <Text className="font-bold text-lg mb-1 text-indigo-500">
                {nutrients.calories}
              </Text>
              <Text className="font-normal text-xs text-muted-foreground">
                Calories
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View className="flex-row px-5 mb-6 gap-3">
          <TouchableOpacity
            className="flex-1 bg-primary flex-row items-center justify-center py-4 px-6 rounded-xl"
            onPress={openCamera}
          >
            <Camera size={20} color="white" />
            <Text className="font-semibold text-base text-primary-foreground ml-2">
              Scan Barcode
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-1 bg-card border border-primary flex-row items-center justify-center py-4 px-6 rounded-xl"
            onPress={() => openModal('addFood')}
          >
            <Plus size={20} className="text-primary" />
            <Text className="font-semibold text-base text-primary ml-2">
              Add Food
            </Text>
          </TouchableOpacity>
        </View>

        {/* Meal Sections */}
        {['breakfast', 'lunch', 'dinner', 'snack'].map(mealType => (
          <View key={mealType} className="px-5 mb-6">
            <View className="flex-row items-center mb-3">
              <Utensils size={20} className="text-primary" />
              <Text className="font-semibold text-lg text-foreground ml-2">
                {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
              </Text>
            </View>
            <View className="bg-card rounded-xl overflow-hidden">
              {getMealLogs(mealType).length === 0 ? (
                <Text className="font-normal text-sm text-muted-foreground text-center p-5">
                  No food logged yet
                </Text>
              ) : (
                getMealLogs(mealType).map(log => (
                  <View key={log.id} className="p-4">
                    <View className="flex-row justify-between items-center mb-1">
                      <Text className="font-semibold text-base text-foreground">
                        {log.foodItem.name}
                      </Text>
                      {log.foodItem.isLiverFriendly ? (
                        <CheckCircle size={16} color="#10B981" />
                      ) : (
                        <XCircle size={16} color="#EF4444" />
                      )}
                    </View>
                    <Text className="font-normal text-sm text-muted-foreground mb-2">
                      {log.foodItem.quantity}
                    </Text>
                    <View className="mb-2">
                      <Text className="font-normal text-xs text-muted-foreground">
                        {log.foodItem.sodium}mg sodium • {log.foodItem.protein}g
                        protein • {log.foodItem.fat}g fat
                      </Text>
                    </View>
                    {log.foodItem.warnings.length > 0 && (
                      <View className="flex-row items-center p-2 bg-red-50 dark:bg-red-950/20 rounded-lg">
                        <AlertTriangle size={14} color="#EF4444" />
                        <Text className="font-normal text-xs text-red-600 dark:text-red-400 ml-1.5 flex-1">
                          {log.foodItem.warnings[0]}
                        </Text>
                      </View>
                    )}
                  </View>
                ))
              )}
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Camera Modal */}
      <Modal visible={modals.camera} animationType="slide">
        <View className="flex-1">
          {permission?.granted ? (
            <CameraView className="flex-1" onBarcodeScanned={handleBarcodeScan}>
              <View className="flex-1 justify-center items-center bg-black/50">
                <Text className="font-medium text-lg text-white text-center mb-8">
                  Point camera at barcode to scan
                </Text>
                <TouchableOpacity
                  className="bg-white px-6 py-3 rounded-lg"
                  onPress={() => closeModal('camera')}
                >
                  <Text className="font-medium text-base text-black">
                    Close
                  </Text>
                </TouchableOpacity>
              </View>
            </CameraView>
          ) : (
            <View className="flex-1 justify-center items-center p-5">
              <Text className="font-normal text-base text-foreground text-center mb-6">
                Camera permission is required to scan barcodes
              </Text>
              <TouchableOpacity
                className="bg-primary px-6 py-3 rounded-lg"
                onPress={requestPermission}
              >
                <Text className="font-medium text-base text-primary-foreground">
                  Grant Permission
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Modal>

      {/* Add Food Modal */}
      <Modal visible={modals.addFood} animationType="slide">
        <SafeAreaView className="flex-1 bg-background">
          <View className="flex-row justify-between items-center px-5 py-4 border-b border-border">
            <Text className="font-bold text-xl text-foreground">Add Food</Text>
            <TouchableOpacity onPress={() => closeModal('addFood')}>
              <Text className="font-medium text-base text-primary">Cancel</Text>
            </TouchableOpacity>
          </View>

          {/* Meal Type Selector */}
          <View className="flex-row px-5 py-4">
            {['breakfast', 'lunch', 'dinner', 'snack'].map(meal => (
              <TouchableOpacity
                key={meal}
                className={`flex-1 py-2 px-3 rounded-lg mx-1 border ${
                  selectedMealType === meal
                    ? 'bg-primary border-primary'
                    : 'bg-card border-border'
                }`}
                onPress={() => setSelectedMealType(meal as any)}
              >
                <Text
                  className={`font-medium text-sm text-center ${
                    selectedMealType === meal
                      ? 'text-primary-foreground'
                      : 'text-muted-foreground'
                  }`}
                >
                  {meal.charAt(0).toUpperCase() + meal.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Search */}
          <View className="flex-row items-center mx-5 px-4 py-3 bg-card rounded-xl mb-4">
            <Search size={20} className="text-muted-foreground" />
            <TextInput
              className="flex-1 font-normal text-base text-foreground ml-3"
              placeholder="Search for food..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#9CA3AF"
            />
          </View>

          {/* Food List */}
          <ScrollView className="flex-1 px-5">
            {filteredFoods.map(food => (
              <TouchableOpacity
                key={food.id}
                className="p-4 bg-card rounded-xl mb-3"
                onPress={() => addFoodToLog(food)}
              >
                <View className="flex-row justify-between items-center mb-1">
                  <Text className="font-semibold text-base text-foreground">
                    {food.name}
                  </Text>
                  {food.isLiverFriendly ? (
                    <CheckCircle size={20} color="#10B981" />
                  ) : (
                    <XCircle size={20} color="#EF4444" />
                  )}
                </View>
                <Text className="font-normal text-sm text-muted-foreground mb-2">
                  {food.quantity}
                </Text>
                <View className="mb-2">
                  <Text className="font-normal text-xs text-muted-foreground">
                    {food.sodium}mg sodium • {food.protein}g protein •{' '}
                    {food.fat}g fat
                  </Text>
                </View>
                {food.warnings.length > 0 && (
                  <View className="flex-row items-center p-2 bg-red-50 dark:bg-red-950/20 rounded-lg">
                    <AlertTriangle size={14} color="#EF4444" />
                    <Text className="font-normal text-xs text-red-600 dark:text-red-400 ml-1.5 flex-1">
                      {food.warnings[0]}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}
