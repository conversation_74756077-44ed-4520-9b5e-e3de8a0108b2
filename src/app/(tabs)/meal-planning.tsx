import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Modal } from 'react-native';
import {
  Plus,
  ChefHat,
  TriangleAlert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CircleCheck as CheckCircle,
  Clock,
  Users,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface MealPlan {
  id: string;
  name: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  ingredients: string[];
  instructions: string[];
  nutrition: {
    sodium: number;
    protein: number;
    fat: number;
    calories: number;
  };
  isLiverFriendly: boolean;
  prepTime: number;
  servings: number;
  warnings: string[];
}

interface WeeklyPlan {
  [key: string]: {
    [mealType: string]: MealPlan | null;
  };
}

export default function MealPlanningScreen() {
  const [selectedDay, setSelectedDay] = useState('Monday');
  const [showAddMeal, setShowAddMeal] = useState(false);
  const [selectedMealType, setSelectedMealType] = useState<
    'breakfast' | 'lunch' | 'dinner' | 'snack'
  >('breakfast');
  const [weeklyPlan, setWeeklyPlan] = useState<WeeklyPlan>({});

  const daysOfWeek = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];
  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

  // Sample liver-friendly meal plans
  const sampleMealPlans: MealPlan[] = [
    {
      id: '1',
      name: 'Grilled Chicken with Steamed Vegetables',
      mealType: 'lunch',
      ingredients: [
        '150g chicken breast',
        '1 cup broccoli',
        '1 cup carrots',
        '1 tbsp olive oil',
        'Fresh herbs (rosemary, thyme)',
      ],
      instructions: [
        'Season chicken breast with herbs',
        'Grill chicken for 6-7 minutes each side',
        'Steam vegetables until tender',
        'Drizzle with olive oil and serve',
      ],
      nutrition: {
        sodium: 125,
        protein: 35,
        fat: 8,
        calories: 285,
      },
      isLiverFriendly: true,
      prepTime: 25,
      servings: 1,
      warnings: [],
    },
    {
      id: '2',
      name: 'Oatmeal with Fresh Berries',
      mealType: 'breakfast',
      ingredients: [
        '1/2 cup rolled oats',
        '1 cup low-fat milk',
        '1/2 cup mixed berries',
        '1 tsp honey',
        '1 tbsp chopped walnuts',
      ],
      instructions: [
        'Cook oats with milk until creamy',
        'Top with fresh berries',
        'Drizzle with honey',
        'Sprinkle with chopped walnuts',
      ],
      nutrition: {
        sodium: 85,
        protein: 12,
        fat: 6,
        calories: 245,
      },
      isLiverFriendly: true,
      prepTime: 10,
      servings: 1,
      warnings: [],
    },
    {
      id: '3',
      name: 'Baked Salmon with Quinoa',
      mealType: 'dinner',
      ingredients: [
        '120g salmon fillet',
        '1/2 cup quinoa',
        '1 cup spinach',
        '1 tbsp lemon juice',
        '1 tsp olive oil',
      ],
      instructions: [
        'Bake salmon at 375°F for 15 minutes',
        'Cook quinoa according to package directions',
        'Sauté spinach until wilted',
        'Serve salmon over quinoa and spinach',
        'Drizzle with lemon juice',
      ],
      nutrition: {
        sodium: 95,
        protein: 28,
        fat: 12,
        calories: 320,
      },
      isLiverFriendly: true,
      prepTime: 30,
      servings: 1,
      warnings: [],
    },
    {
      id: '4',
      name: 'Processed Deli Sandwich',
      mealType: 'lunch',
      ingredients: [
        '2 slices white bread',
        '100g processed deli meat',
        '2 slices processed cheese',
        '1 tbsp mayo',
      ],
      instructions: [
        'Layer meat and cheese on bread',
        'Add mayo and close sandwich',
      ],
      nutrition: {
        sodium: 1450,
        protein: 25,
        fat: 18,
        calories: 420,
      },
      isLiverFriendly: false,
      prepTime: 5,
      servings: 1,
      warnings: [
        'Very high sodium content',
        'Processed meats may worsen liver inflammation',
      ],
    },
  ];

  const addMealToPlan = (meal: MealPlan) => {
    setWeeklyPlan(prev => ({
      ...prev,
      [selectedDay]: {
        ...prev[selectedDay],
        [selectedMealType]: meal,
      },
    }));
    setShowAddMeal(false);
  };

  const removeMealFromPlan = (day: string, mealType: string) => {
    setWeeklyPlan(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [mealType]: null,
      },
    }));
  };

  const getDayNutrition = (day: string) => {
    const dayPlan = weeklyPlan[day] || {};
    return Object.values(dayPlan).reduce(
      (total, meal) => {
        if (meal) {
          return {
            sodium: total.sodium + meal.nutrition.sodium,
            protein: total.protein + meal.nutrition.protein,
            fat: total.fat + meal.nutrition.fat,
            calories: total.calories + meal.nutrition.calories,
          };
        }
        return total;
      },
      { sodium: 0, protein: 0, fat: 0, calories: 0 }
    );
  };

  const todayNutrition = getDayNutrition(selectedDay);

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="px-5 pt-4">
          <Text className="text-3xl font-bold text-foreground">
            Meal Planning
          </Text>
          <Text className="text-base text-muted-foreground mt-1">
            Plan liver-friendly meals
          </Text>
        </View>

        {/* Day Selector */}
        <View className="px-5 mt-6">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {daysOfWeek.map(day => (
              <TouchableOpacity
                key={day}
                className={`px-4 py-2 mr-3 rounded-lg border ${
                  selectedDay === day
                    ? 'bg-primary border-primary'
                    : 'bg-card border-border'
                }`}
                onPress={() => setSelectedDay(day)}
              >
                <Text
                  className={`text-sm font-medium ${
                    selectedDay === day
                      ? 'text-primary-foreground'
                      : 'text-muted-foreground'
                  }`}
                >
                  {day.slice(0, 3)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Daily Nutrition Summary */}
        <View className="mx-5 mt-6 p-4 bg-card rounded-xl">
          <Text className="text-lg font-semibold text-foreground mb-4">
            {selectedDay} Nutrition
          </Text>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text
                className={`text-lg font-bold ${
                  todayNutrition.sodium > 2000
                    ? 'text-destructive'
                    : 'text-success'
                }`}
              >
                {todayNutrition.sodium}mg
              </Text>
              <Text className="text-sm text-muted-foreground">Sodium</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-primary">
                {todayNutrition.protein}g
              </Text>
              <Text className="text-sm text-muted-foreground">Protein</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-warning">
                {todayNutrition.fat}g
              </Text>
              <Text className="text-sm text-muted-foreground">Fat</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-info">
                {todayNutrition.calories}
              </Text>
              <Text className="text-sm text-muted-foreground">Calories</Text>
            </View>
          </View>
        </View>

        {/* Meal Plan for Selected Day */}
        <View className="px-5 mt-6">
          <Text className="text-xl font-semibold text-foreground mb-4">
            Meals for {selectedDay}
          </Text>
          {mealTypes.map(mealType => {
            const meal = weeklyPlan[selectedDay]?.[mealType];
            return (
              <View key={mealType} className="mb-4">
                <View className="flex-row justify-between items-center mb-3">
                  <View className="flex-row items-center">
                    <ChefHat size={20} className="text-primary" />
                    <Text className="text-lg font-medium text-foreground ml-2">
                      {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                    </Text>
                  </View>
                  <TouchableOpacity
                    className="w-8 h-8 bg-card border border-primary rounded-lg items-center justify-center"
                    onPress={() => {
                      setSelectedMealType(mealType as any);
                      setShowAddMeal(true);
                    }}
                  >
                    <Plus size={16} className="text-primary" />
                  </TouchableOpacity>
                </View>

                {meal ? (
                  <TouchableOpacity
                    className="p-4 bg-card rounded-xl border border-border"
                    onLongPress={() =>
                      removeMealFromPlan(selectedDay, mealType)
                    }
                  >
                    <View className="flex-row justify-between items-center mb-3">
                      <Text className="text-lg font-semibold text-foreground flex-1">
                        {meal.name}
                      </Text>
                      <View className="ml-2">
                        {meal.isLiverFriendly ? (
                          <CheckCircle size={16} className="text-success" />
                        ) : (
                          <AlertTriangle
                            size={16}
                            className="text-destructive"
                          />
                        )}
                      </View>
                    </View>

                    <View className="flex-row mb-3">
                      <View className="flex-row items-center mr-4">
                        <Clock size={14} className="text-muted-foreground" />
                        <Text className="text-sm text-muted-foreground ml-1">
                          {meal.prepTime} min
                        </Text>
                      </View>
                      <View className="flex-row items-center">
                        <Users size={14} className="text-muted-foreground" />
                        <Text className="text-sm text-muted-foreground ml-1">
                          {meal.servings} serving
                        </Text>
                      </View>
                    </View>

                    <View>
                      <Text className="text-sm text-muted-foreground">
                        {meal.nutrition.sodium}mg sodium •{' '}
                        {meal.nutrition.protein}g protein •{' '}
                        {meal.nutrition.calories} cal
                      </Text>
                    </View>

                    {meal.warnings.length > 0 && (
                      <View className="mt-3 p-2 bg-destructive/10 rounded-lg flex-row items-center">
                        <AlertTriangle size={14} className="text-destructive" />
                        <Text className="text-sm text-destructive ml-2 flex-1">
                          {meal.warnings[0]}
                        </Text>
                      </View>
                    )}
                  </TouchableOpacity>
                ) : (
                  <View className="p-4 bg-muted/50 border border-dashed border-border rounded-xl">
                    <Text className="text-sm text-muted-foreground text-center">
                      No meal planned
                    </Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>

        {/* Weekly Overview */}
        <View className="px-5 mt-6 mb-6">
          <Text className="text-xl font-semibold text-foreground mb-4">
            Weekly Overview
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {daysOfWeek.map(day => {
              const dayNutrition = getDayNutrition(day);
              const plannedMeals = Object.values(weeklyPlan[day] || {}).filter(
                Boolean
              ).length;

              return (
                <View
                  key={day}
                  className="w-20 p-3 mr-3 bg-card rounded-xl items-center"
                >
                  <Text className="text-sm font-medium text-foreground mb-1">
                    {day.slice(0, 3)}
                  </Text>
                  <Text className="text-xs text-muted-foreground mb-2">
                    {plannedMeals}/4 meals
                  </Text>
                  <Text
                    className={`text-xs font-medium ${
                      dayNutrition.sodium > 2000
                        ? 'text-destructive'
                        : 'text-success'
                    }`}
                  >
                    {dayNutrition.sodium}mg Na
                  </Text>
                </View>
              );
            })}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Add Meal Modal */}
      <Modal visible={showAddMeal} animationType="slide">
        <SafeAreaView className="flex-1 bg-background">
          <View className="flex-row justify-between items-center p-5 border-b border-border">
            <Text className="text-xl font-semibold text-foreground">
              Add{' '}
              {selectedMealType.charAt(0).toUpperCase() +
                selectedMealType.slice(1)}
            </Text>
            <TouchableOpacity onPress={() => setShowAddMeal(false)}>
              <Text className="text-base font-medium text-primary">Cancel</Text>
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1 px-5 pt-4">
            {sampleMealPlans
              .filter(meal => meal.mealType === selectedMealType)
              .map(meal => (
                <TouchableOpacity
                  key={meal.id}
                  className="p-4 mb-4 bg-card rounded-xl border border-border"
                  onPress={() => addMealToPlan(meal)}
                >
                  <View className="flex-row justify-between items-center mb-3">
                    <Text className="text-lg font-semibold text-foreground flex-1">
                      {meal.name}
                    </Text>
                    <View className="ml-2">
                      {meal.isLiverFriendly ? (
                        <CheckCircle size={20} className="text-success" />
                      ) : (
                        <AlertTriangle size={20} className="text-destructive" />
                      )}
                    </View>
                  </View>

                  <View className="flex-row mb-3">
                    <View className="flex-row items-center mr-4">
                      <Clock size={14} className="text-muted-foreground" />
                      <Text className="text-sm text-muted-foreground ml-1">
                        {meal.prepTime} min
                      </Text>
                    </View>
                    <View className="flex-row items-center">
                      <Users size={14} className="text-muted-foreground" />
                      <Text className="text-sm text-muted-foreground ml-1">
                        {meal.servings} serving
                      </Text>
                    </View>
                  </View>

                  <View className="mb-3">
                    <Text className="text-sm text-muted-foreground">
                      {meal.nutrition.sodium}mg sodium •{' '}
                      {meal.nutrition.protein}g protein •{' '}
                      {meal.nutrition.calories} cal
                    </Text>
                  </View>

                  <View>
                    <Text className="text-sm font-medium text-foreground mb-2">
                      Ingredients:
                    </Text>
                    {meal.ingredients.slice(0, 3).map(ingredient => (
                      <Text
                        key={ingredient}
                        className="text-sm text-muted-foreground"
                      >
                        • {ingredient}
                      </Text>
                    ))}
                    {meal.ingredients.length > 3 && (
                      <Text className="text-sm text-muted-foreground/70 mt-1">
                        +{meal.ingredients.length - 3} more ingredients
                      </Text>
                    )}
                  </View>

                  {meal.warnings.length > 0 && (
                    <View className="mt-3 p-2 bg-destructive/10 rounded-lg flex-row items-center">
                      <AlertTriangle size={14} className="text-destructive" />
                      <Text className="text-sm text-destructive ml-2 flex-1">
                        {meal.warnings[0]}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}
