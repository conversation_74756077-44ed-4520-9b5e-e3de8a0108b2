import React, { useState } from 'react';
import { View, TouchableOpacity, Platform, Modal } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Calendar, CircleAlert as AlertCircle } from 'lucide-react-native';
import { format } from 'date-fns';
import { useIsDarkTheme } from '@/lib/store/selectors';
import { Text } from '@/components/ui/text';
import { cn } from '@/lib/utils';

interface DatePickerProps {
  readonly label: string;
  readonly value: Date | null | undefined;
  readonly onChange: (date: Date) => void;
  readonly error?: string;
  readonly required?: boolean;
  readonly minimumDate?: Date;
  readonly maximumDate?: Date;
  readonly accessibilityLabel?: string;
}

export default function DatePicker({
  label,
  value,
  onChange,
  error,
  minimumDate,
  maximumDate,
  accessibilityLabel,
  required = false,
}: DatePickerProps) {
  const isDark = useIsDarkTheme();
  const [showPicker, setShowPicker] = useState(false);

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (selectedDate) {
      onChange(selectedDate);
    }
  };

  const formatDisplayDate = (date: Date | null | undefined) => {
    if (!date) return 'Select date';
    return format(new Date(date), 'MMM dd, yyyy');
  };

  const showDatePicker = () => {
    setShowPicker(true);
  };

  const hideDatePicker = () => {
    setShowPicker(false);
  };

  return (
    <View className="mb-5">
      <Text className="text-base font-medium text-foreground mb-2">
        {label}
        {required && (
          <Text className="text-base font-medium text-destructive"> *</Text>
        )}
      </Text>

      <TouchableOpacity
        className={cn(
          'flex-row items-center justify-between border rounded-lg px-3 py-3.5 min-h-[48px] bg-background',
          error ? 'border-destructive' : 'border-border'
        )}
        onPress={showDatePicker}
        accessibilityLabel={accessibilityLabel ?? `${label} date picker`}
        accessibilityHint="Tap to select a date"
        accessibilityRole="button"
      >
        <Text
          className={cn(
            'text-base',
            value ? 'text-foreground' : 'text-muted-foreground'
          )}
        >
          {formatDisplayDate(value)}
        </Text>
        <Calendar size={20} className="text-muted-foreground" />
      </TouchableOpacity>

      {error && (
        <View className="flex-row items-center mt-2">
          <AlertCircle size={16} className="text-destructive" />
          <Text className="text-sm text-destructive ml-1.5 flex-1">
            {error}
          </Text>
        </View>
      )}

      {showPicker && (
        <>
          {Platform.OS === 'ios' && (
            <View className="absolute inset-0 bg-black/50 justify-end z-50">
              <View className="bg-background px-4 py-3 items-end">
                <TouchableOpacity onPress={hideDatePicker}>
                  <Text className="text-base font-medium text-primary">
                    Done
                  </Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={value || new Date()}
                mode="date"
                display="spinner"
                onChange={handleDateChange}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
                themeVariant={isDark ? 'dark' : 'light'}
              />
            </View>
          )}

          {Platform.OS === 'android' && (
            <DateTimePicker
              value={value || new Date()}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
            />
          )}

          <Modal
            transparent
            animationType="slide"
            visible={showPicker}
            onRequestClose={() => setShowPicker(false)}
          >
            <View className="flex-1 bg-black/50 justify-center items-center">
              <View className="w-[90%] max-w-[400px] bg-card rounded-xl p-5 max-h-[80%]">
                <View className="flex-row justify-between items-center mb-5">
                  <Text className="text-lg font-semibold text-foreground">
                    Select {label}
                  </Text>
                  <TouchableOpacity
                    onPress={() => setShowPicker(false)}
                    className="p-2"
                  >
                    <Text className="text-base font-medium text-primary">
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>

                <WebDatePicker
                  value={value}
                  onChange={onChange}
                  minimumDate={minimumDate}
                  maximumDate={maximumDate}
                />
              </View>
            </View>
          </Modal>
        </>
      )}
    </View>
  );
}

// Web-specific date picker component
function WebDatePicker({
  value,
  onChange,
  minimumDate,
  maximumDate,
}: Readonly<{
  value: Date | null | undefined;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
}>) {
  const handleDateChange = (dateString: string) => {
    const date = new Date(dateString);
    console.log('dateString', dateString, date);
    if (!isNaN(date.getTime())) {
      onChange(date);
    }
  };

  const date = React.useMemo(() => {
    return value ? format(new Date(value), 'yyyy-MM-dd') : '';
  }, [value]);

  return (
    <View className="mb-5">
      <input
        type="date"
        value={date}
        onChange={e => handleDateChange(e.target.value)}
        // min={minimumDate ? format(minimumDate, 'yyyy-MM-dd') : undefined}
        // max={maximumDate ? format(maximumDate, 'yyyy-MM-dd') : undefined}
        style={{
          width: '100%',
          padding: 12,
          fontSize: 16,
          borderRadius: 8,
          border: '1px solid hsl(var(--border))',
          backgroundColor: 'hsl(var(--card))',
          color: 'hsl(var(--foreground))',
        }}
      />
    </View>
  );
}
