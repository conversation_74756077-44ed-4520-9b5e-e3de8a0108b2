import React, { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useThemePreference } from '@/lib/store/selectors';
import { useColorScheme as useNativeWindColorScheme } from 'nativewind';
import { getNavTheme } from '@/lib/theme';
import { ThemeProvider as NavThemeProvider } from '@react-navigation/native';

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Simplified ThemeProvider that handles:
 * 1. NativeWind theme system based on user preferences
 * 2. System color scheme detection for 'system' preference
 * 3. Navigation theme integration
 * 4. Status bar styling
 *
 * Uses NativeWind as the single source of truth for theming
 */
export function ThemeProvider({ children }: Readonly<ThemeProviderProps>) {
  const themePreference = useThemePreference();
  const systemColorScheme = useColorScheme();
  const { colorScheme, setColorScheme } = useNativeWindColorScheme();

  // Determine the effective theme based on user preference
  const effectiveTheme =
    themePreference === 'system'
      ? (systemColorScheme ?? 'light')
      : themePreference;

  const isDark = effectiveTheme === 'dark';

  // Sync user preference with NativeWind
  useEffect(() => {
    if (colorScheme !== effectiveTheme) {
      setColorScheme(effectiveTheme);
    }
  }, [effectiveTheme, colorScheme, setColorScheme]);

  const navTheme = getNavTheme(isDark);

  return (
    <NavThemeProvider value={navTheme}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      {children}
    </NavThemeProvider>
  );
}
