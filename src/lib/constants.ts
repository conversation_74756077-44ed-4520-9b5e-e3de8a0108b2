import { Platform } from "react-native";

/**
 * Design system constants for react-native-reusables
 */

// Default sizes for components
export const SIZES = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  "2xl": 24,
  "3xl": 30,
  "4xl": 36,
} as const;

// Border radius values
export const RADIUS = {
  none: 0,
  sm: 2,
  base: 4,
  md: 6,
  lg: 8,
  xl: 12,
  "2xl": 16,
  "3xl": 24,
  full: 9999,
} as const;

// Spacing values
export const SPACING = {
  0: 0,
  px: 1,
  0.5: 2,
  1: 4,
  1.5: 6,
  2: 8,
  2.5: 10,
  3: 12,
  3.5: 14,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  11: 44,
  12: 48,
  14: 56,
  16: 64,
  20: 80,
  24: 96,
  28: 112,
  32: 128,
  36: 144,
  40: 160,
  44: 176,
  48: 192,
  52: 208,
  56: 224,
  60: 240,
  64: 256,
  72: 288,
  80: 320,
  96: 384,
} as const;

// Platform-specific adjustments
export const PLATFORM_ADJUSTMENTS = {
  button: {
    height: Platform.OS === "ios" ? 44 : 48, // iOS HIG vs Material Design
    fontSize: Platform.OS === "ios" ? 17 : 16,
  },
  input: {
    height: Platform.OS === "ios" ? 44 : 48,
    fontSize: Platform.OS === "ios" ? 17 : 16,
  },
  text: {
    fontSize: Platform.OS === "ios" ? 17 : 16,
  },
} as const;

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  fast: 150,
  normal: 250,
  slow: 400,
} as const;

// Z-index layers
export const Z_INDEX = {
  base: 0,
  tooltip: 1000,
  popover: 1010,
  modal: 1020,
  dropdown: 1030,
  toast: 1040,
} as const;

// Accessibility constants
export const ACCESSIBILITY = {
  minimumTouchTarget: 44, // iOS HIG minimum
  defaultRole: "button" as const,
  defaultHitSlop: { top: 8, bottom: 8, left: 8, right: 8 },
} as const;

/**
 * CSS variable names for theme colors
 * These will be mapped to actual color values in the theme system
 */
export const CSS_VAR_NAMES = {
  // Base colors
  background: "--background",
  foreground: "--foreground",

  // Card colors
  card: "--card",
  cardForeground: "--card-foreground",

  // Popover colors
  popover: "--popover",
  popoverForeground: "--popover-foreground",

  // Primary colors
  primary: "--primary",
  primaryForeground: "--primary-foreground",

  // Secondary colors
  secondary: "--secondary",
  secondaryForeground: "--secondary-foreground",

  // Muted colors
  muted: "--muted",
  mutedForeground: "--muted-foreground",

  // Accent colors
  accent: "--accent",
  accentForeground: "--accent-foreground",

  // Destructive colors
  destructive: "--destructive",
  destructiveForeground: "--destructive-foreground",

  // Border and input colors
  border: "--border",
  input: "--input",
  ring: "--ring",

  // Chart colors
  chart1: "--chart-1",
  chart2: "--chart-2",
  chart3: "--chart-3",
  chart4: "--chart-4",
  chart5: "--chart-5",
} as const;

export const NAV_THEME = {
  light: {
    background: "hsl(0 0% 100%)", // background
    border: "hsl(240 5.9% 90%)", // border
    card: "hsl(0 0% 100%)", // card
    notification: "hsl(0 84.2% 60.2%)", // destructive
    primary: "hsl(240 5.9% 10%)", // primary
    text: "hsl(240 10% 3.9%)", // foreground
  },
  dark: {
    background: "hsl(240 10% 3.9%)", // background
    border: "hsl(240 3.7% 15.9%)", // border
    card: "hsl(240 10% 3.9%)", // card
    notification: "hsl(0 72% 51%)", // destructive
    primary: "hsl(0 0% 98%)", // primary
    text: "hsl(0 0% 98%)", // foreground
  },
};

export type CSSVarName = keyof typeof CSS_VAR_NAMES;
