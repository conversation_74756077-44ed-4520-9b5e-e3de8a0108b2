import { NAV_THEME } from "./constants";
import {
  DarkTheme,
  DefaultTheme,
  Theme as NavigationTheme,
} from "@react-navigation/native";

// Navigation theme objects
export const LIGHT_NAV_THEME: NavigationTheme = {
  ...DefaultTheme,
  colors: NAV_THEME.light,
};

export const DARK_NAV_THEME: NavigationTheme = {
  ...DarkTheme,
  colors: NAV_THEME.dark,
};

// Navigation theme getter function
export function getNavTheme(isDark: boolean) {
  return isDark ? DARK_NAV_THEME : LIGHT_NAV_THEME;
}
