import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Platform-aware class merging utility.
 * Handles React Native web styling by removing web-only classes on native platforms.
 */
export function platformCn(...inputs: ClassValue[]) {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const Platform = require("react-native").Platform;

  if (Platform.OS === "web") {
    return cn(...inputs);
  }

  // On native platforms, filter out web-specific classes
  const filtered = inputs.map((input) => {
    if (typeof input === "string") {
      return input
        .split(" ")
        .filter((cls) => !cls.startsWith("web:"))
        .join(" ");
    }
    return input;
  });

  return cn(...filtered);
}

/**
 * Utility to create responsive values for React Native
 * @param base Base value for all platforms
 * @param native Native-specific value
 * @param web Web-specific value
 */
export function createResponsiveValue<T>(
  base: T,
  native?: T,
  web?: T,
): T {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const Platform = require("react-native").Platform;

  if (Platform.OS === "web" && web !== undefined) {
    return web;
  }

  if (Platform.OS !== "web" && native !== undefined) {
    return native;
  }

  return base;
}
