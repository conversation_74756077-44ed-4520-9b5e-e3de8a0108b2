@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors - synced with Zustand theme */
    --background: 220 14% 96%; /* #F8FAFC */
    --foreground: 220 13% 18%; /* #1F2937 */

    --card: 0 0% 100%; /* #FFFFFF */
    --card-foreground: 220 13% 18%; /* #1F2937 */

    --popover: 0 0% 100%; /* #FFFFFF */
    --popover-foreground: 220 13% 18%; /* #1F2937 */

    --primary: 174 84% 40%; /* #14B8A6 */
    --primary-foreground: 0 0% 100%; /* #FFFFFF */

    --secondary: 214 32% 91%; /* #F1F5F9 */
    --secondary-foreground: 220 13% 18%; /* #1F2937 */

    --muted: 214 32% 91%; /* #F1F5F9 */
    --muted-foreground: 220 9% 46%; /* #6B7280 */

    --accent: 214 32% 91%; /* #F1F5F9 */
    --accent-foreground: 220 13% 18%; /* #1F2937 */

    --destructive: 0 84% 60%; /* #EF4444 */
    --destructive-foreground: 0 0% 100%; /* #FFFFFF */

    --border: 220 13% 91%; /* #E5E7EB */
    --input: 220 13% 91%; /* #E5E7EB */
    --ring: 174 84% 40%; /* #14B8A6 */

    /* Status colors */
    --success: 142 76% 36%; /* #10B981 */
    --warning: 38 92% 50%; /* #F59E0B */
    --info: 217 91% 60%; /* #3B82F6 */

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme colors - synced with Zustand theme */
    --background: 222 47% 11%; /* #0F172A */
    --foreground: 220 14% 96%; /* #F8FAFC */

    --card: 215 28% 17%; /* #1E293B */
    --card-foreground: 220 14% 96%; /* #F8FAFC */

    --popover: 215 28% 17%; /* #1E293B */
    --popover-foreground: 220 14% 96%; /* #F8FAFC */

    --primary: 174 84% 40%; /* #14B8A6 */
    --primary-foreground: 220 13% 18%; /* #1F2937 */

    --secondary: 215 25% 27%; /* #334155 */
    --secondary-foreground: 220 14% 96%; /* #F8FAFC */

    --muted: 215 25% 27%; /* #334155 */
    --muted-foreground: 215 16% 47%; /* #CBD5E1 */

    --accent: 215 25% 27%; /* #334155 */
    --accent-foreground: 220 14% 96%; /* #F8FAFC */

    --destructive: 0 84% 60%; /* #EF4444 */
    --destructive-foreground: 220 14% 96%; /* #F8FAFC */

    --border: 215 20% 35%; /* #475569 */
    --input: 215 20% 35%; /* #475569 */
    --ring: 174 84% 40%; /* #14B8A6 */

    /* Status colors */
    --success: 142 76% 36%; /* #10B981 */
    --warning: 38 92% 50%; /* #F59E0B */
    --info: 217 91% 60%; /* #3B82F6 */

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family:
      system-ui,
      -apple-system,
      sans-serif;
  }
}

/* Additional NativeWind compatibility styles */
@layer components {
  .container {
    @apply mx-auto px-4;
  }
}
