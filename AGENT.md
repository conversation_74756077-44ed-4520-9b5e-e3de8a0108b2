# AGENT.md

## Commands

- **Dev server**: `bun dev` (Expo with telemetry disabled)
- **Lint**: `bun run lint` (Expo's built-in ESLint)
- **Build for web**: `bun run build:web`
- **Run single test**: `bun test tests/auth.test.js` (Jest-style in tests/ directory)

## Architecture

- **Stack**: React Native + Expo Router + TypeScript + Zustand + Supabase + Clerk Auth
- **Package Manager**: Bun (use `bun` commands, not npm/yarn)
- **State**: Zustand slices in `src/lib/store/` - AuthSlice, NutritionSlice, MedicationSlice, SettingsSlice, UISlice
- **Routing**: File-based with `src/app/` (tabs), auth screens in `(auth)/`
- **Database**: Supabase + SQLite offline, comprehensive medical data validation

## Code Style

- **Imports**: Use `@/` prefix for absolute imports from src/
- **Forms**: Zod schemas + React Hook Form with `zodResolver`
- **Styling**: React Native StyleSheet, avoid inline styles
- **Error Handling**: Use `handleSupabaseError()` utility for all async operations
- **Types**: Strict TypeScript, generated Supabase types in `types/database.types.ts`
- **Naming**: camelCase for variables/functions, PascalCase for components/types
- **State Access**: Use granular selectors from `src/lib/store/selectors.ts` for performance
- **Medical Domain**: Handle liver conditions, medications, lab results with proper validation

## Key Rules from CLAUDE.md

- Follow SonarQube standards: DRY principle, functions <15 lines, proper error handling
- Use Zustand slices, not React Context for app state
- Medical data requires comprehensive validation and security
- Persist only essential data to prevent storage bloat (last 30 days of logs)
